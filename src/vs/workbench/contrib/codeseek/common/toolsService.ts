import { CancellationToken, CancellationTokenSource } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { QueryBuilder } from '../../../services/search/common/queryBuilder.js';
import { ISearchService } from '../../../services/search/common/search.js';
import { TerminalCommandId } from '../../terminal/common/terminal.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { ICtagsSymbolService } from '../browser/ctagsSymbolService.js';
import { SymbolDefinition } from '../electron-main/ctags/ctagsRunner.js';
import { IClangdSymbolService } from '../browser/clangdSymbolService.js';
import { ICodeseekFileService } from './codeseekFileService.js';
import { filenameToVscodeLanguage } from './helpers/detectLanguage.js';
import { convertFilePathToUri, getRelativePath, getWorkspaceUri } from './helpers/path.js';
import {
	ApproveRequestResultType, AskReponseType, AskResponse, codeseekTools,
	DirectoryItem, ToolCallParamsType, ToolCallReturnType, ToolFns, ToolName,
	ToolNameEnum, ToolResultToString, ToolCallStatus, ToolCallResultType, ToolCallType
} from './toolsServiceTypes.js';
import { LogLevel } from '../../../../platform/log/common/log.js';
import { AskMessage, IChatThreadService, PluginMessageOpts, userMessageOpts } from '../browser/chatThreadType.js';
import { IPluginTaskService } from '../browser/pluginTaskService.js';
import pWaitFor from '../../../common/pWaitFor.js';
import { ICodebaseRemoteService } from './codebaseRemoteService.js';
import { ICodebaseService, ISearchResult } from './codebaseTypes.js';
import { CodebaseSelection } from './selectedFileService.js';
import { stringifyCodebaseSelections } from './prompt/prompts.js';
import { IEditCodeService } from '../browser/editCodeService.js';
import { ITerminalToolService } from '../browser/terminalToolService.js';

const MAX_CHILDREN_URIs_PAGE = 500;
const computeDirectoryResult = async (
	fileService: IFileService,
	rootURI: URI,
	pageNumber: number = 1
): Promise<ToolCallReturnType[ToolNameEnum.LIST_FILES]> => {
	const stat = await fileService.resolve(rootURI, { resolveMetadata: false });
	if (!stat.isDirectory) {
		return { rootURI, children: null, hasPrevPage: false, itemsRemaining: 0 };
	}

	const originalChildrenLength = stat.children?.length ?? 0;
	const fromChildIdx = MAX_CHILDREN_URIs_PAGE * (pageNumber - 1);
	const toChildIdx = MAX_CHILDREN_URIs_PAGE * pageNumber - 1; // INCLUSIVE
	const listChildren = stat.children?.slice(fromChildIdx, toChildIdx + 1) ?? [];

	const children: DirectoryItem[] = listChildren.map(child => ({
		uri: child.resource,
		name: child.name,
		isDirectory: child.isDirectory,
		isSymbolicLink: child.isSymbolicLink || false
	}));

	const hasPrevPage = pageNumber > 1;
	const itemsRemaining = Math.max(0, originalChildrenLength - (toChildIdx + 1));

	return {
		rootURI,
		children,
		hasPrevPage,
		itemsRemaining
	};
};

// this is just for ease of readability
export const tripleTick = ['```', '```'];

const fileContentsToString = (result: ToolCallReturnType[ToolNameEnum.READ_FILE], workspaceService: IWorkspaceContextService): string => {
	const relativePath = getRelativePath(workspaceService, result.uri.fsPath);
	return `\
${relativePath}
${tripleTick[0]}${filenameToVscodeLanguage(result.uri.fsPath) ?? ''}
${result.fileContents}
${tripleTick[1]}
`;
};
const directoryResultToString = (result: ToolCallReturnType[ToolNameEnum.LIST_FILES]): string => {
	if (!result.children) {
		return `Error: ${result.rootURI} is not a directory`;
	}

	let output = '';
	const entries = result.children;

	if (!result.hasPrevPage) {
		output += `${result.rootURI}\n`;
	}

	for (let i = 0; i < entries.length; i++) {
		const entry = entries[i];
		const isLast = i === entries.length - 1;
		const prefix = isLast ? '└── ' : '├── ';

		output += `${prefix}${entry.name}${entry.isDirectory ? '/' : ''}${entry.isSymbolicLink ? ' (symbolic link)' : ''}\n`;
	}

	return output;
};

const validateQueryStr = (queryStr: unknown) => {
	if (typeof queryStr !== 'string') throw new Error('Error calling tool: provided query must be a string.');
	return queryStr;
};


// TODO!!!! check to make sure in workspace
const validateURI = (uriStr: unknown, _workspaceContextService: IWorkspaceContextService) => {
	if (typeof uriStr !== 'string') throw new Error('Error calling tool: provided uri must be a string.');
	return convertFilePathToUri(uriStr, _workspaceContextService);
};

const validatePageNum = (pageNumberUnknown: unknown) => {
	const proposedPageNum = Number.parseInt(pageNumberUnknown + '');
	const num = Number.isInteger(proposedPageNum) ? proposedPageNum : 1;
	const pageNumber = num < 1 ? 1 : num;
	return pageNumber;
};

export interface IToolsService {
	readonly _serviceBrand: undefined;
	toolFns: ToolFns;
	toolResultToString: ToolResultToString;
	isNeedApprove(toolName: ToolName): boolean;

	findSymbolCtagsDefinitions(symbolName: string): Promise<SymbolDefinition[]>;
	executeTool(containerId: string, threadId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType>;
	cancelExecTool(threadId: string): void;
}

export const IToolsService = createDecorator<IToolsService>('ToolsService');

export class ToolsService implements IToolsService {

	readonly _serviceBrand: undefined;

	public toolFns: ToolFns;
	public toolResultToString: ToolResultToString;
	private readonly threadId2CancelTokens: Map<string, CancellationTokenSource> = new Map();

	constructor(
		@IFileService fileService: IFileService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@ISearchService searchService: ISearchService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@ICodeseekFileService codeseekFileService: ICodeseekFileService,
		@ICommandService commandService: ICommandService,
		@IModelService modelService: IModelService,
		@ICtagsSymbolService private readonly _ctagsSymbolService: ICtagsSymbolService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@IClangdSymbolService private readonly _clangdSymbolService: IClangdSymbolService,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@IPluginTaskService private readonly _pluginTaskService: IPluginTaskService,
		@ICodebaseRemoteService private readonly codebaseRemoteService: ICodebaseService,
		@ITerminalToolService private readonly flowTerminalService: ITerminalToolService,
	) {
		_codeseekLogService.setLevel(LogLevel.Info);
		const queryBuilder = instantiationService.createInstance(QueryBuilder);
		this.toolFns = {
			[ToolNameEnum.READ_FILE]: async (p: ToolCallParamsType[ToolNameEnum.READ_FILE], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { path: uriStr } = p;

				const uri = validateURI(uriStr, this._workspaceContextService);
				const isExist = await fileService.exists(uri);
				if (!isExist) {
					throw new Error(`Could not find file '${uri.fsPath}' in workspace`);
				}
				this._codeseekLogService.info('read file', uri.fsPath);
				const readFileContents = await codeseekFileService.readFile(uri);
				const startLine = 1;
				let lines = 0;
				if (readFileContents) {
					const model = modelService.getModel(uri);
					if (model) {
						lines = model.getLineCount();
					} else {
						lines = (readFileContents.match(/\n/g) || []).length + (readFileContents.length > 0 ? 1 : 0);
					}
				}
				return { uri, fileContents: readFileContents, startLine, endLine: lines };
			},
			[ToolNameEnum.EDIT_FILE]: async (p: ToolCallParamsType[ToolNameEnum.EDIT_FILE], callback?: () => any) => {
				const { path, changeStr } = p;

				const uri = validateURI(path, this._workspaceContextService);

				const isExist = await fileService.exists(uri);
				if (!isExist) {
					throw new Error(`Could not find file '${uri.fsPath}' in workspace`);
				}
				const uuid = crypto.randomUUID();
				const opts = {
					from: 'ClickApply',
					type: 'searchReplace',
					applyStr: changeStr,
					uri,
					applyBoxId: uuid,
				} as const;
				// 延迟获取 IEditCodeService 以避免循环依赖
				const editCodeService = this.instantiationService.invokeFunction(accessor => accessor.get(IEditCodeService));
				await editCodeService.startApplying(opts);
				return;
			},
			[ToolNameEnum.LIST_FILES]: async (p: ToolCallParamsType[ToolNameEnum.LIST_FILES], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { path: pathStr } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);

				const dirResult = await computeDirectoryResult(fileService, uri);

				return dirResult;
			},
			[ToolNameEnum.PATHNAME_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.PATHNAME_SEARCH], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { query: queryUnknown } = p;

				const queryStr = validateQueryStr(queryUnknown);

				const query = queryBuilder.file(this._workspaceContextService.getWorkspace().folders.map(f => f.uri), { filePattern: queryStr, });
				const data = await searchService.fileSearch(query, CancellationToken.None);

				const uris = data.results
					.map(({ resource, results }) => resource);
				if (typeof uris === 'string') {
					return { queryStr, uris: [uris] };
				}
				return { queryStr, uris };
			},
			[ToolNameEnum.SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.SEARCH], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { query: queryUnknown } = p;

				const queryStr = validateQueryStr(queryUnknown);

				const query = queryBuilder.text({ pattern: queryStr, }, this._workspaceContextService.getWorkspace().folders.map(f => f.uri));
				const data = await searchService.textSearch(query, CancellationToken.None);

				const uris = data.results
					.map(({ resource, results }) => resource);
				if (typeof uris === 'string') {
					return { queryStr, uris: [uris] };
				}
				return { queryStr, uris };
			},
			[ToolNameEnum.CREATE_FILE]: async (p: ToolCallParamsType[ToolNameEnum.CREATE_FILE], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { path: pathStr } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);

				return { uri };
			},
			[ToolNameEnum.UPDATE_FILE]: async (p: ToolCallParamsType[ToolNameEnum.UPDATE_FILE], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { path: pathStr, content: contentStr, start: startUnknown, end: endUnknown } = p;

				const uri = validateURI(pathStr, this._workspaceContextService);
				const start = validatePageNum(startUnknown);
				const end = validatePageNum(endUnknown);

				const res = await codeseekFileService.updateFile(uri, contentStr + '', start, end);

				return { content: res, uri };
			},
			[ToolNameEnum.APPROVE_REQUEST]: async (p: ToolCallParamsType[ToolNameEnum.APPROVE_REQUEST], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { command } = p;
				const response: AskResponse = await callback?.();
				if (response.response === AskReponseType.yesButtonClicked) {
					if (command) {
						commandService.executeCommand(TerminalCommandId.SendSequence, command);
					}
					return { content: '用户同意此操作', response: AskReponseType.yesButtonClicked };
				}
				else if (response.response === AskReponseType.noButtonClicked) {
					return { content: '用户拒绝此操作', response: AskReponseType.noButtonClicked };
				}
				else {
					throw new Error('Invalid response type');
				}
			},
			[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: async (p: ToolCallParamsType[ToolNameEnum.ASK_FOLLOWUP_QUESTION], callback?: () => any, cancellationToken?: CancellationToken) => {
				const response: AskResponse = await callback?.();
				if (response.response === AskReponseType.messageResponse) {
					return { content: response.text as string, response: AskReponseType.messageResponse };
				}
				else {
					throw new Error('Invalid response type');
				}
			},
			[ToolNameEnum.CTAGS_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CTAGS_QUERY], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { symbol: symbolStr } = p;

				if (typeof symbolStr !== 'string') throw new Error('Error calling tool: provided symbol must be a string.');
				const symbol = symbolStr;
				const definitions = await this.findSymbolCtagsDefinitions(symbol);
				_codeseekLogService.info('ctags definitions result:', JSON.stringify(definitions));
				return definitions.map(def => ({
					rawLineContent: def.rawLineContent,
					name: def.name,
					path: def.path,
					scopePath: def.scopePath,
					line: def.line,
					kind: def.kind,
					language: def.language,
					positions: def.positions
				}));
			},
			[ToolNameEnum.CLANGD_QUERY]: async (p: ToolCallParamsType[ToolNameEnum.CLANGD_QUERY], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { filePath, line, character } = p;

				if (typeof filePath !== 'string') throw new Error('Error calling tool: provided filePath must be a string.');
				if (typeof line !== 'number') throw new Error('Error calling tool: provided line must be a number.');
				if (typeof character !== 'number') throw new Error('Error calling tool: provided character must be a number.');

				const uri = convertFilePathToUri(filePath, this._workspaceContextService);
				const result = await this._clangdSymbolService.getSymbolReferences(uri, line, character);
				return result;
			},
			[ToolNameEnum.SHOW_SUMMARY]: async (p: ToolCallParamsType[ToolNameEnum.SHOW_SUMMARY], callback?: () => any) => {
			},
			[ToolNameEnum.SHOW_CONTENT]: async (p: ToolCallParamsType[ToolNameEnum.SHOW_CONTENT], callback?: () => any) => {
				const content = "success to show content"
				return { content };
			},
			[ToolNameEnum.CODEBASE_SEARCH]: async (p: ToolCallParamsType[ToolNameEnum.CODEBASE_SEARCH], callback?: () => any, cancellationToken?: CancellationToken) => {
				const { query } = p;
				const workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
				if (!workspaceUri) {
					throw new Error('Could not find workspace');
				}
				const results = await this.codebaseRemoteService.search({ userQuery: query, repoUri: workspaceUri });
				return results;
			},
			[ToolNameEnum.EXEC_COMMAND]: async (p: ToolCallParamsType[ToolNameEnum.EXEC_COMMAND], callback?: () => any, cancellationToken?: CancellationToken) => {
				let workDir: string | undefined = undefined;
				if (p.workdir !== undefined) {
					const uri = validateURI(p.workdir, this._workspaceContextService);
					workDir = uri.fsPath;
				}
				const output = await this.flowTerminalService.runCommandOnNewInstance(p.command, workDir, cancellationToken)
				return { output };

			}
		};


		this.toolResultToString = {
			[ToolNameEnum.READ_FILE]: (result) => {
				const fileContents = fileContentsToString(result, this._workspaceContextService);
				return fileContents;
			},
			[ToolNameEnum.EDIT_FILE]: (result) => {
				return '';
			},
			[ToolNameEnum.LIST_FILES]: (result) => {
				const dirTreeStr = directoryResultToString(result);
				return dirTreeStr;
			},
			[ToolNameEnum.PATHNAME_SEARCH]: (result) => {
				return result.uris.map(uri => uri.fsPath).join('\n');
			},
			[ToolNameEnum.SEARCH]: (result) => {
				return result.uris.map(uri => uri.fsPath).join('\n');
			},
			[ToolNameEnum.CREATE_FILE]: (result) => {
				return "File created successfully";
			},
			[ToolNameEnum.UPDATE_FILE]: (result) => {
				return result.content;
			},
			[ToolNameEnum.APPROVE_REQUEST]: (result) => {
				return result.content;
			},
			[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: (result) => {
				return result.content;
			},
			[ToolNameEnum.CTAGS_QUERY]: (result) => {
				if (!Array.isArray(result)) return 'Error: Invalid result format';
				return JSON.stringify(result);
			},
			[ToolNameEnum.CLANGD_QUERY]: (result) => {
				if (!Array.isArray(result)) return 'Error: Invalid result format';
				return JSON.stringify(result);
			},
			[ToolNameEnum.SHOW_SUMMARY]: () => {
				return 'success to show'
			},
			[ToolNameEnum.SHOW_CONTENT]: (result) => {
				return result.content
			},
			[ToolNameEnum.CODEBASE_SEARCH]: (result: ISearchResult[]) => {
				const codebaseSelections: CodebaseSelection[] = result.map(r => ({
					type: 'Codebase' as const,
					fileURI: r.uri,
					title: 'Codebase',
					selectionStr: r.content,
					range: r.range,
					fromMention: false
				}));
				const workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;
				const codebaseStr = stringifyCodebaseSelections(codebaseSelections, workspaceUri?.fsPath || '');
				return codebaseStr || '';
			},
			[ToolNameEnum.EXEC_COMMAND]: (result) => {
				return result.output
			}
		};
	}

	isNeedApprove(toolName: ToolName) {
		return codeseekTools[toolName].needApprove;
	}

	/**
	 * 使用 ctags 查找符号定义
	 * @param symbolName 要查找的符号名称
	 * @param filter 可选的过滤条件
	 * @returns 找到的符号定义数组
	 */
	async findSymbolCtagsDefinitions(symbolName: string): Promise<SymbolDefinition[]> {
		try {
			// 获取当前工作区的所有根目录
			const workspaceFolders = this._workspaceContextService.getWorkspace().folders;
			if (!workspaceFolders.length) {
				return [];
			}

			// 将所有工作区文件夹作为搜索范围
			const scopeDirs = workspaceFolders.map(folder => folder.uri);

			// 调用 ctags 服务进行符号查找
			const results = await this._ctagsSymbolService.getSymbolDefinitions(symbolName, scopeDirs);
			return results;
		} catch (error) {
			this._codeseekLogService.error('查找符号定义失败:', error);
			return [];
		}
	}

	async executeTool(containerId: string, threadId: string, toolCall: ToolCallType, userMessageOpts: userMessageOpts): Promise<ToolCallResultType> {
		const toolName = toolCall.name as ToolName;
		let toolResultVal: ToolCallReturnType[ToolName];
		let content: string = '';
		const ideTool = this.toolFns[toolName];

		const onWait = async () => {
			this._chatThreadService.setStreamState(containerId, threadId, { isStreaming: false });
			const askMessage: AskMessage = {
				type: 'tool',
				content: toolCall,
			};
			this._chatThreadService.getCurrentThread(containerId).state.askMessage = askMessage;
			await pWaitFor(() => this._chatThreadService.getCurrentThread(containerId).state.askResponse !== undefined, { interval: 100 });
			const currentThreadState = this._chatThreadService.getCurrentThread(containerId).state;
			const response = { type: currentThreadState.askResponse!.type, response: currentThreadState.askResponse?.response, text: currentThreadState.askResponse?.text };
			currentThreadState.askResponse = undefined;
			currentThreadState.askResponseText = undefined;
			return response;
		};
		if (ideTool === undefined) {
			let externalToolsNeedApprove = true;
			const filteredTools = (userMessageOpts as PluginMessageOpts).taskInfo?.externalTools?.filter(tool => tool.toolName === toolName);
			if (filteredTools && filteredTools.length > 0) {
				externalToolsNeedApprove = filteredTools[0].needApprove;
			}
			if (externalToolsNeedApprove) {
				toolResultVal = await this.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.yesButtonClicked && userMessageOpts.from === 'Plugin') {
					this._pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				}
				return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: toolResultVal };
			}
			this._chatThreadService.setStreamState(containerId, threadId, { isStreaming: true });
			if (userMessageOpts.from === 'Plugin') {
				this._pluginTaskService.fireToolCall((userMessageOpts as PluginMessageOpts).taskInfo.taskId, toolName, toolCall.params);
				return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: null };
			}
		}

		try {
			if (this.isNeedApprove(toolName)) {
				toolResultVal = await this.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				content = this.toolResultToString[toolName](toolResultVal as any);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.noButtonClicked) {
					return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: toolResultVal };
				}
			}
			this._chatThreadService.setStreamState(containerId, threadId, { isStreaming: true });
			const cancellationSource = new CancellationTokenSource();
			this.threadId2CancelTokens.set(threadId, cancellationSource);
			toolResultVal = await ideTool(toolCall.params as any, undefined, cancellationSource.token);
			this.threadId2CancelTokens.delete(threadId);
			cancellationSource.dispose();
			content = this.toolResultToString[toolName](toolResultVal as any);
			return { status: ToolCallStatus.success, name: toolName, error: '', content, toolCallReturn: toolResultVal };
		} catch (error) {
			return { status: ToolCallStatus.failure, name: toolName, error: error.message, content, toolCallReturn: null };
		}
	}
	cancelExecTool(threadId: string): void {
		const cancelToken = this.threadId2CancelTokens.get(threadId);
		if (cancelToken) {
			cancelToken.cancel();
		}
	}
}

registerSingleton(IToolsService, ToolsService, InstantiationType.Delayed);
