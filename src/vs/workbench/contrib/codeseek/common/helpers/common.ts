import { ToolNameEnum, ToolCallType } from '../toolsServiceTypes.js';


export const findLastIndex = <T>(arr: T[], condition: (t: T) => boolean): number => {
	for (let i = arr.length - 1; i >= 0; i--) {
		if (condition(arr[i])) {
			return i;
		}
	}
	return -1;
};


export const removeToolTags = (data: string): string => {
	// 创建所有工具名称的正则表达式模式
	const toolNames = Object.values(ToolNameEnum);
	const toolNamesPattern = [...toolNames, 'final_answer', 'thinking', 'goal', 'summary'].join('|');

	let newText = data;

	// 1. 处理完整闭合的标签
	const closedTagRegex = new RegExp(`<(${toolNamesPattern})>[\\s\\S]*?<\\/\\1>`, 'gi');
	newText = newText.replace(closedTagRegex, '');

	// 2. 处理未闭合的完整开始标签（从开始标签到字符串末尾）
	const openTagRegex = new RegExp(`<(${toolNamesPattern})>[\\s\\S]*$`, 'gi');
	newText = newText.replace(openTagRegex, '');

	// 3. 处理不完整的标签（如 <xx 或 < ）
    newText = newText.replace(/<[^>]*$/g, '');

	return newText;
}

export const extractTool = (text: string): ToolCallType[] => {
	const toolCalls: ToolCallType[] = [];

	// 创建所有工具名称的正则表达式模式
	const toolNames = Object.values(ToolNameEnum);
	const toolNamesPattern = toolNames.join('|');

	// 匹配工具调用的正则表达式：<tool_name>...</tool_name>
	const toolCallRegex = new RegExp(`<(${toolNamesPattern})>([\\s\\S]*?)<\\/\\1>`, 'gi');

	let match;
	while ((match = toolCallRegex.exec(text)) !== null) {
		const toolName = match[1] as ToolNameEnum;
		const toolContent = match[2];

		// 提取参数
		const params = extractParameters(toolContent);

		// 根据工具名称映射参数到正确的结构
		const mappedParams = mapToolParameters(toolName, params);

		toolCalls.push({
			name: toolName,
			params: mappedParams,
			id: `${toolName}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
		});
	}

	return toolCalls;
};

// 提取XML标签内的参数
const extractParameters = (content: string): Record<string, string> => {
	const params: Record<string, string> = {};

	// 匹配参数标签：<param_name>value</param_name>
	const paramRegex = /<([^>]+)>([\s\S]*?)<\/\1>/g;

	let match;
	while ((match = paramRegex.exec(content)) !== null) {
		const paramName = match[1].trim();
		const paramValue = match[2].trim();
		params[paramName] = paramValue;
	}

	return params;
};

// 根据工具名称映射参数到正确的ToolCallParamsType结构
const mapToolParameters = (toolName: ToolNameEnum, params: Record<string, string>): any => {
	switch (toolName) {
		case ToolNameEnum.READ_FILE:
			return { path: params.uri || params.path || '' };

		case ToolNameEnum.LIST_FILES:
			return { path: params.uri || params.path || '' };

		case ToolNameEnum.PATHNAME_SEARCH:
			return { query: params.query || '' };

		case ToolNameEnum.SEARCH:
			return { query: params.query || '' };

		case ToolNameEnum.CREATE_FILE:
			return { path: params.uri || params.path || '' };

		case ToolNameEnum.UPDATE_FILE:
			return {
				path: params.uri || params.path || '',
				content: params.content || '',
				start: Number(params.start) || 0,
				end: Number(params.end) || 0
			};

		case ToolNameEnum.APPROVE_REQUEST:
			return {
				content: params.content || '',
				command: params.command || ''
			};

		case ToolNameEnum.ASK_FOLLOWUP_QUESTION:
			return { question: params.question || '' };

		case ToolNameEnum.CTAGS_QUERY:
			return { symbol: params.symbol || '' };

		case ToolNameEnum.CLANGD_QUERY:
			return {
				filePath: params.filePath || '',
				line: Number(params.line) || 0,
				character: Number(params.character) || 0
			};

		case ToolNameEnum.SHOW_SUMMARY:
			return {
				summary: params.summary || '',
				detail: params.detail || ''
			};

		case ToolNameEnum.SHOW_CONTENT:
			return {
				content: params.content || ''
				};

		case ToolNameEnum.CODEBASE_SEARCH:
			return { query: params.query || '' };

		default:
			// 对于未知工具，返回原始参数
			return params;
	}
};

// 定义 final_answer 解析结果类型
export type ChatAgentFinalAnswerResult = {
	content: string;
	reference: string;
};

export type CodeAgentFinalAnswerResult = {
	content: string;
	summary: string;
}

// 解析 final_answer XML 结构
export const extractChatAgentFinalAnswer = (text: string): ChatAgentFinalAnswerResult => {
	// 匹配 <final_answer>...</final_answer> 标签
	const finalAnswerRegex = /<final_answer>([\s\S]*?)<\/final_answer>/gi;
	const finalAnswerMatch = finalAnswerRegex.exec(text);

	if (!finalAnswerMatch) {
		return { content: '', reference: '' };
	}

	const finalAnswerContent = finalAnswerMatch[1];

	// 提取 <content> 标签内容
	const contentRegex = /<content>([\s\S]*?)<\/content>/gi;
	const contentMatch = contentRegex.exec(finalAnswerContent);
	const content = contentMatch ? contentMatch[1].trim() : '';

	// 提取 <reference> 标签内容
	const referenceRegex = /<reference>([\s\S]*?)<\/reference>/gi;
	const referenceMatch = referenceRegex.exec(finalAnswerContent);
	const reference = referenceMatch ? referenceMatch[1].trim() : '';

	return {
		content,
		reference
	};
};
