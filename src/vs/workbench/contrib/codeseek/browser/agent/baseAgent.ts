import { Disposable } from '../../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { LLMChatMessage } from '../../common/llmMessageTypes.js';
import { ChatMessage, IChatThreadService } from '../chatThreadType.js';
import { ToolCallResultType } from '../../common/toolsServiceTypes.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';

export const MAX_MESSAGES_SENT = 25;

export class BaseAgent extends Disposable {

	private readonly _onDidAbort = new Emitter<string>();
	readonly onDidAbort: Event<string> = this._onDidAbort.event;

	AGENT_END_TAG = "<final_answer>";
	FORMAT_ERROR_MESSAGE = `你的回答格式不正确, 提示：任务结束使用 ${this.AGENT_END_TAG} 标签， 工具调用需使用'工具集'中的给定的标签，请重新回答。`;
	TOOL_FORMAT_ERROR_MESSAGE = `你调用的工具不在工具集中，请重新回答。`;

	chatThreadService: IChatThreadService;

	constructor(
		@IInstantiationService readonly instantiationService: IInstantiationService,
	) {
		super();
		this.chatThreadService = instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
	}

	public async handleText(text: string, containerId: string, threadId: string) {
		let processedLength = 0;
		const streamInterval = 50;
		const chunkSize = 3;
		let accumulatedText = '';

		while (processedLength < text.length) {
			const endIndex = Math.min(processedLength + chunkSize, text.length);
			const newChunk = text.substring(processedLength, endIndex);
			processedLength = endIndex;
			accumulatedText += newChunk;
			this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: accumulatedText });
			await new Promise(resolve => setTimeout(resolve, streamInterval));
		}
	}

	public extractFinalAnswer(text: string): string {
		const finalAnswerRegex = /<final_answer>([\s\S]*?)<\/final_answer>/gi;
		const finalAnswerMatch = finalAnswerRegex.exec(text);
		return finalAnswerMatch ? finalAnswerMatch[1].trim() : '';
	}

	public extractThinking(text: string): string {
		const thinkingRegex = /<thinking>([\s\S]*?)<\/thinking>/gi;
		const thinkingMatch = thinkingRegex.exec(text);
		return thinkingMatch ? thinkingMatch[1].trim() : '';
	}

	public extractSummary(text: string): string {
		const summaryRegex = /<summary>([\s\S]*?)<\/summary>/gi;
		const summaryMatch = summaryRegex.exec(text);
		return summaryMatch ? summaryMatch[1].trim() : '';
	}

	public extractGoal(text: string): string {
		const goalRegex = /<goal>([\s\S]*?)<\/goal>/gi;
		const goalMatch = goalRegex.exec(text);
		return goalMatch ? goalMatch[1].trim() : '';
	}

	public extractContent(text: string): string {
		const contentRegex = /<content>([\s\S]*?)<\/content>/gi;
		const contentMatch = contentRegex.exec(text);
		return contentMatch ? contentMatch[1].trim() : '';
	}

	public extractReference(text: string): string {
		const referenceRegex = /<reference>([\s\S]*?)<\/reference>/gi;
		const referenceMatch = referenceRegex.exec(text);
		return referenceMatch ? referenceMatch[1].trim() : '';
	}

	public joinToolResult(toolResult: ToolCallResultType[]) {
		return toolResult.map(tool => tool.content).join('\n\n');
	}

	public toLLMChatMessage(c: ChatMessage): LLMChatMessage {
		if (c.role === 'system' || c.role === 'user') {
			return { role: c.role, content: c.content || '(empty message)' };
		}
		else if (c.role === 'assistant')
			return { role: c.role, content: c.content || '(empty message)' };
		else if (c.role === 'tool')
			return { role: 'user', content: c.content || '(empty output)' };
		else {
			throw 1;
		}
	};

	public extractShowContent(text: string): string {
		const referenceRegex = /<show_content>([\s\S]*?)<\/show_content>/gi;
		const referenceMatch = referenceRegex.exec(text);
		return referenceMatch ? referenceMatch[1].trim() : '';
	}
}
