import { ITodolistService, Step } from '../todolistService.js';
import { IToolsService } from '../../common/toolsService.js';
import { ChatMessage, userMessageOpts } from '.././chatThreadType.js';
import { ToolCallStatus, ToolCallResultType, ToolNameEnum } from '../../common/toolsServiceTypes.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { ILLMMessageService } from '../../common/llmMessageService.js';
import { FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { CodeAgentConvertMessageOpts, IConvertToLLMMessageService } from '../convertToLLMMessageService.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IAgentManageService } from './agentManageService.js';
import { SendLLMType } from '../../common/llmMessageTypes.js';
import { BaseAgent, MAX_MESSAGES_SENT } from './baseAgent.js';

export type CodeAgentState = {
	[threadId: string]: ChatMessage[];
}

export type OsInformation = {
	operatingSystem: 'windows' | 'mac' | 'linux' | null;
	defaultShell: string;
}

export interface ICodeAgent {
	readonly _serviceBrand: undefined;

	act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts): Promise<void>;
}

export class CodeAgentService extends BaseAgent implements ICodeAgent {
	readonly _serviceBrand: undefined;
	private state: CodeAgentState = {};

	private internalTools: ToolNameEnum[] = [
		ToolNameEnum.READ_FILE,
		ToolNameEnum.LIST_FILES,
		ToolNameEnum.PATHNAME_SEARCH,
		ToolNameEnum.SEARCH,
		ToolNameEnum.CODEBASE_SEARCH,

	];

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IToolsService private readonly toolsService: IToolsService,
		@ITodolistService private readonly todolistService: ITodolistService,
		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
		@IConvertToLLMMessageService private readonly convertToLLMMessageService: IConvertToLLMMessageService,
		@IInstantiationService override readonly instantiationService: IInstantiationService,
	) {
		super(instantiationService);
		this.instantiationService.invokeFunction(accessor => {
			const agentManageService = accessor.get(IAgentManageService);
			this._register(agentManageService.onDidAbort(cancelToken => {
				this.abort(cancelToken);
			}));
		});
	}

	private async preparePrompt(containerId: string, threadId: string, step: Step, userMessageOpts: userMessageOpts) {
		const convertMessageOpts: CodeAgentConvertMessageOpts = {
			messagesType: 'codeAgentMessages',
			containerId,
			threadId,
			task: this.todolistService.state[threadId].task,
			taskContext: this.todolistService.state[threadId].taskContext,
			step,
			userMessageOpts,
			internalTools: this.internalTools,
		};
		const prompt = await this.convertToLLMMessageService.prepareLLMChatMessages(convertMessageOpts);
		return prompt;
	}

	public async act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts) {
		this.todolistService.markStep(threadId, stepIndex, 'in_progress');
		const steps = this.todolistService.getAllStep(threadId);
		if (!steps) return
		const prompt = await this.preparePrompt(containerId, threadId, steps[stepIndex], userMessageOpts);
		const agentLoop = async () => {

			let shouldSendAnotherMessage = true;
			let nAttempts = 0;
			this.state[threadId] = [
				...prompt.userPrompts.map(p => ({ role: 'user', content: p } as ChatMessage)),
			];

			while (shouldSendAnotherMessage) {
				shouldSendAnotherMessage = false;
				if (nAttempts > MAX_MESSAGES_SENT) {
					this.todolistService.markStep(threadId, stepIndex, 'blocked');
					this.chatThreadService.setStreamState(containerId, threadId, {
						streamingToken: undefined,
						isStreaming: false,
						error: {
							message: `Tool exceeded max call count(${MAX_MESSAGES_SENT}).`,
							fullError: null,
							isExceededMaxLoopCount: true
						}
					});
					break;
				}
				nAttempts += 1;
				let res_: () => void;
				const awaitable = new Promise<void>((res, rej) => { res_ = res; });

				let messages = this.state[threadId].map(m => (this.toLLMChatMessage(m)));
				const chatMessages: SendLLMType = {
					messagesType: 'chatMessages',
					messages: [
						{ role: 'system', content: prompt.systemPrompt },
						...messages,
					],
				}
				let toolCallResults: ToolCallResultType[] = [];
				const cancelToken = this.llmMessageService.sendLLMMessage({
					containerId,
					...chatMessages,
					useProviderFor: FeatureNames.CtrlL,
					logging: { loggingName: userMessageOpts.chatMode },
					onText: ({ fullText }) => { },
					onFinalMessage: async ({ fullText, toolCalls }) => {
						this.logger.info(`Final message, threadId: ${threadId}, fullText: `, fullText, `toolCalls: `, JSON.stringify(toolCalls));
						let fullText_ = ''
						const summary = this.extractSummary(fullText);
						const thinking = this.extractThinking(fullText);
						const showContent = this.extractShowContent(fullText);
						if (thinking) {
							fullText_ += thinking;
						}
						if (summary) {
							fullText_ += summary;
						}
						if (showContent) {
							fullText_ += showContent;
						}
						if (fullText_.length > 0) {
							this.handleText(fullText_, containerId, threadId);
						}
						this.state[threadId].push({ role: 'assistant', content: fullText, displayContent: fullText });

						if ((toolCalls?.length ?? 0) === 0) {
							if (fullText.includes(this.AGENT_END_TAG)) {
								const finalAnswer = this.extractFinalAnswer(fullText);
								this.chatThreadService.finishStreamingTextMessage(containerId, threadId, fullText_, { message: '', fullError: null }, false);
								this.todolistService.markStep(threadId, stepIndex, 'completed', { content: finalAnswer, summary });
								this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: undefined, isStreaming: false });
							} else {
								this.state[threadId].push({ role: 'user', content: this.FORMAT_ERROR_MESSAGE, displayContent: this.FORMAT_ERROR_MESSAGE, state: {} } as ChatMessage);
								shouldSendAnotherMessage = true;
							}
						}
						else {
							this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText_, displayContent: fullText }, false);
							this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: undefined });
							const isCallFormatOK = toolCalls?.every(toolCall => this.internalTools.includes(toolCall.name as ToolNameEnum));
							if (!isCallFormatOK) {
								this.state[threadId].push({ role: 'user', content: this.TOOL_FORMAT_ERROR_MESSAGE, displayContent: this.TOOL_FORMAT_ERROR_MESSAGE, state: {} } as ChatMessage);
								shouldSendAnotherMessage = true;
							} else {
								for (const toolCall of toolCalls ?? []) {
									this.chatThreadService.setStreamState(containerId, threadId, {
										tool: {
											toolCall,
											toolCallResult: {
												status: ToolCallStatus.executing,
												name: toolCall.name as ToolNameEnum,
												content: '',
												error: undefined,
												toolCallReturn: null,
											},
										}
									});
									const toolCallResult: ToolCallResultType = await this.toolsService.executeTool(containerId, threadId, toolCall, userMessageOpts);
									toolCallResults.push(toolCallResult);
									this.chatThreadService.setStreamState(containerId, threadId, {
										tool: {
											toolCall,
											toolCallResult,
										}
									});
									this.chatThreadService.addMessageToThread(containerId, threadId, {
										role: 'tool',
										content: toolCallResult.content,
										toolCall: toolCall,
										toolCallResult: toolCallResult,
									}, true);
									shouldSendAnotherMessage = true;
									this.chatThreadService.setStreamState(containerId, threadId, { tool: undefined });
									await new Promise(resolve => setTimeout(resolve, 1000));
								}
								const content = this.joinToolResult(toolCallResults);
								this.state[threadId].push({ role: 'tool', content: content } as ChatMessage);
							}
						}
						res_();
					},
					onError: (error) => {
						this.todolistService.markStep(threadId, stepIndex, 'failed', { content: '', summary: '' });
						this.chatThreadService.finishStreamingTextMessage(containerId, threadId, this.chatThreadService.streamState[containerId]?.[threadId]?.messageSoFar ?? '', error);
						res_();
					},
				});
				if (cancelToken === null) break;
				this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isStreaming: true });

				await awaitable;
			}
		};

		agentLoop();
	}

	public abort(cancelToken: string) {
		this.logger.info(`CodeAgentService: aborting requestId: ${cancelToken}`);
		this.llmMessageService.abort(cancelToken);
	}

}
