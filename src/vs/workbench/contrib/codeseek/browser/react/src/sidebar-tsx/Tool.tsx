import React, {useState } from 'react';
import { ToolMessage } from '../../.././chatThreadType.js';
import { ToolCallResultType, ToolCallReturnType, ToolCallStatus, ToolCallType, ToolNameEnum } from '../../../../../../../workbench/contrib/codeseek/common/toolsServiceTypes.js';
import { useAccessor } from '../util/services.js';
import { getWorkspaceUri } from '../../../../common/helpers/path.js';
import { ChatMarkdownRender } from '../markdown/ChatMarkdownRender.js';
import { ChevronRight, Pencil, X } from 'lucide-react';
import { ChatMessageLocation } from '../../../aiRegexService.js';
import { ToolCallParamsType, ToolName } from '../../../../common/toolsServiceTypes.js';
import { FileKind } from '../../../../../../../platform/files/common/files.js';
import { URI } from '../../../../../../../base/common/uri.js';
import { VSCodeFileIcon } from './SidebarChat.js';


export const getBasename = (pathStr: string) => {
	// 'unixify' path
	pathStr = pathStr.replace(/[/\\]+/g, '/') // replace any / or \ or \\ with /
	const parts = pathStr.split('/') // split on /
	return parts[parts.length - 1]
}

export type FileItemListProps = {
	files: {
		uri: URI,
		range?: {
			startLineNumber: number;
			startColumn: number;
			endLineNumber: number;
			endColumn: number
		},
	}[]
	className: {
		hover: string,
		normal: string
	}
}

export const FileItemList = ({ files, className }: FileItemListProps) => {
	return (
		<div className="w-full max-h-[100px] overflow-y-auto overflow-x-hidden"
			style={{
				scrollbarWidth: 'thin',
				scrollbarColor: 'var(--vscode-scrollbarSlider-background) transparent'
			}}>
			{files.map(({ uri, range }, i) => (
				<FileItem
					key={i}
					uri={uri}
					range={range}
					className={className}
				/>
			))}
		</div>
	)
}

// FileItem组件的类型定义
interface FileItemProps {
	uri: URI;
	range?: {
		startLineNumber: number;
		startColumn: number;
		endLineNumber: number;
		endColumn: number;
	};
	className: {
		hover: string;
		normal: string;
	};
}

// 单独的FileItem组件，每个组件有自己的hover状态
const FileItem = ({ uri, range, className }: FileItemProps) => {
	const [isHovered, setIsHovered] = useState(false)
	const accessor = useAccessor()
	const commandService = accessor.get('ICommandService')
	return (
		<div className="pl-2 py-0.5">
			<div className="flex items-center gap-1">
				<VSCodeFileIcon uri={uri} fileKind={FileKind.FILE} />
				<div
					className={`cursor-pointer`}
					onClick={(e) => {
						e.preventDefault();
						if (uri) {
							commandService.executeCommand('vscode.open', uri, {
								selection: {
									startLineNumber: range?.startLineNumber,
									startColumn: range?.startColumn,
									endLineNumber: range?.endLineNumber,
									endColumn: range?.endColumn
								},
								preserveFocus: false,
								preview: true,
							}).then(() => {
								commandService.executeCommand('revealLine', {
									lineNumber: range?.startLineNumber,
									at: 'center'
								});
							});
						}
					}}
				>
					<div className={`flex items-center w-full overflow-hidden text-ellipsis whitespace-nowrap ${isHovered ? className.hover : className.normal}`}
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						<div className="flex items-center gap-x-1 text-md truncate max-w-[50px]">
							{getBasename(uri.fsPath)}
						</div>
						<div className="flex items-center pl-4 text-xs overflow-hidden opacity-80">
							<div style={{ direction: 'ltr', unicodeBidi: 'embed' }}>{uri.fsPath}</div>
							<div className="ml-[4px] opacity-70">{range ? ` L${range.startLineNumber + 1}-${range.startColumn + 1}` : ''}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

type ToolReusltToComponent = { [T in ToolName]: (props: { message: ToolMessage<T> }, messageIdx: number, isStreaming: boolean, containerId?: string, threadId?: string) => React.ReactNode }
interface ToolResultProps {
	title: string;
	toolCallResult: ToolCallResultType;
	actionNumResults?: number;
	children?: React.ReactNode;
	showParamHtml?: React.ReactElement;
	icon?: string;
	actionParam? : string;
	isStreaming?: boolean;
}

const ToolResult = ({
	title,
	toolCallResult,
	actionNumResults,
	children,
	showParamHtml,
	actionParam,
	icon,
	isStreaming
}: ToolResultProps) => {
	const [isExpanded, setIsExpanded] = useState(false);
	const [isHovered, setIsHovered] = useState(false);

	const isDropdown = !!children

	return toolCallResult.status === ToolCallStatus.failure ? (
		<div className="mx-2 select-none mt-0.5">
			<div className="px-1 py-0.5 flex text-md text-vscode-error-fg opacity-1 transition-opacity duration-100">
				<div className="px-1.5 flex-shrink-0 mt-1">
					<span className="codicon codicon-warning scale-75"
						style={{
							color: 'var(--vscode-errorForeground)',
						}}
					></span>
				</div>
				<div className="py-0.5 flex-grow">
					{toolCallResult.error}
				</div>
			</div>
		</div>
	) : (
		<div className="mx-2 select-none mt-0.5 text-codeseek-fg-3 text-xs cursor-pointer">
			<div className={`px-1 py-0.5 bg-codeseek-bg-tool ${isExpanded ? '' : 'hover:text-codeseek-fg-1'}`}>
				<div
					className={`flex items-center min-h-[20px] ${isDropdown ? 'px-1 hover:brightness-125 duration-150' : 'mx-1'} ${isExpanded ? 'text-codeseek-fg-1' : ''}`}
					style={{
						transition: 'opacity 0.3s ease'
					}}
					onClick={() => children && setIsExpanded(!isExpanded)}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
				>
					<div className="w-5 h-5 flex items-center justify-center flex-shrink-0 mr-1">
						{isDropdown && (isHovered || isExpanded) ? (
							<ChevronRight size={12} style={
									{
										color: 'var(--vscode-foreground)',
										opacity: (isHovered || isExpanded) ? '1' : '0.6',
										transition: 'opacity 0.3s ease'
									}
								}
								className={`transition-transform duration-100 ease-[cubic-bezier(0.4,0,0.2,1)] ${isExpanded ? 'rotate-90' : ''}`}
							/>
						) : icon && (
							<span className={`flex items-center justify-center ${icon}`}
								 style={{ transition: 'opacity 0.3s ease', fontSize: '14px', opacity: (isHovered) ? '1' : '0.6', color: 'var(--vscode-foreground)' }}
							></span>
						)}
					</div>
					<div className="flex items-center flex-wrap gap-x-1 gap-y-0.5 relative flex-grow">
						<span className="text-md">{title}</span>
						{showParamHtml ? (
							<div className="flex items-end">
								{showParamHtml}
							</div>
						) : (
							<span className="flex items-end text-codeseek-fg-4 italic">{`"`}{actionParam}{`"`}</span>
						)}
						{actionNumResults !== undefined && (
							<span className="text-codeseek-fg-4 flex items-end">
								{`(`}{actionNumResults}{` result`}{actionNumResults !== 1 ? 's' : ''}{`)`}
							</span>
						)}
						{toolCallResult.status === ToolCallStatus.executing && (
							<span className="ml-auto mr-2 mt-0.5 animate-spin">
								<span className="codicon codicon-loading" style={{ fontSize: '9px' }}></span>
							</span>
						)}
					</div>
				</div>
			</div>
			<div
				className={`px-2 py-0.5 overflow-hidden transition-all duration-200 ease-in-out ${isExpanded ? 'max-h-[500px] opacity-100 overflow-y-auto text-codeseek-fg-1' : 'max-h-0 opacity-0'}`}
			>
				{children}
			</div>
		</div>
	)
};

export const ToolResultToComponent: ToolReusltToComponent = {
	[ToolNameEnum.READ_FILE]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.READ_FILE]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.READ_FILE]
		const showParamHtml = (
			<div className="flex items-center gap-x-1">
				<VSCodeFileIcon uri={URI.file(params.path)} fileKind={FileKind.FILE} />
				<span className="italic">
					{getBasename(params.path)}{toolCallReturn?.startLine ? `:${toolCallReturn?.startLine}-${toolCallReturn?.endLine}` : ''}
				</span>
			</div>
		)
		return (
			<ToolResult
				icon="codicon codicon-eye"
				title="Read file"
				showParamHtml={showParamHtml}
				toolCallResult={message.toolCallResult}
				isStreaming={isStreaming}
			/>
		)
	},
	[ToolNameEnum.LIST_FILES]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const accessor = useAccessor()
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.LIST_FILES]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.LIST_FILES]
		let folderName = getBasename(params.path)
		if (folderName === '.') {
			const workspaceFolder = getWorkspaceUri(workspaceService).workspaceName
			if (workspaceFolder) {
				folderName = workspaceFolder;
			}
		}
		return <ToolResult
			icon='codicon codicon-folder !text-[14px]'
			title="List folder"
			actionParam={`${folderName}`}
			actionNumResults={toolCallReturn?.children?.length}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			<div>
				{toolCallReturn?.children?.map((item, i) => (
					<div key={i} className="pl-4 mb-[2px]">
						{item.isDirectory ? (
							<div className="flex items-center gap-x-2 px-1">
								<span className="codicon codicon-folder" style={{ fontSize: '10px' }}></span>
								<span>{item.name}/</span>
							</div>
						) : (
							<div className="flex items-center gap-x-1">
								<VSCodeFileIcon uri={item.uri} fileKind={FileKind.FILE} />
								<span>{item.name}</span>
							</div>
						)}
					</div>
				))}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.PATHNAME_SEARCH]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const accessor = useAccessor()
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.PATHNAME_SEARCH]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.PATHNAME_SEARCH]
		return <ToolResult
			icon="codicon codicon-search"
			title="Searched filename"
			actionParam={params.query}
			actionNumResults={Array.isArray(toolCallReturn?.uris) ? toolCallReturn?.uris.length : 0}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			{Array.isArray(toolCallReturn?.uris) ?
				<FileItemList files={toolCallReturn?.uris.map(uri => ({ uri: uri }))} className={{ hover: 'text-codeseek-fg-1', normal: 'text-codeseek-fg-3' }} />
				:
				null
			}
		</ToolResult>
	},
	[ToolNameEnum.SEARCH]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const accessor = useAccessor()
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.SEARCH]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.SEARCH]
		return <ToolResult
			icon="codicon codicon-search"
			title="Searched"
			actionParam={params.query}
			actionNumResults={Array.isArray(toolCallReturn?.uris) ? toolCallReturn?.uris.length : 0}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			{Array.isArray(toolCallReturn?.uris) ?
				<FileItemList files={toolCallReturn?.uris.map(uri => ({ uri: uri }))} className={{ hover: 'text-codeseek-fg-1', normal: 'text-codeseek-fg-3' }} />
				:
				null
			}
		</ToolResult>
	},
	[ToolNameEnum.CREATE_FILE]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CREATE_FILE]
		return <ToolResult
			icon="codicon codicon-file"
			title="Create file"
			actionParam={params.path}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
		</ToolResult>
	},
	[ToolNameEnum.UPDATE_FILE]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.UPDATE_FILE]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.UPDATE_FILE]
		return <ToolResult
			icon="codicon codicon-edit"
			title="Update file"
			actionParam={params.content}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			<div>
				<div className="pl-2 py-0.5 mb-1">
					<a
						href={toolCallReturn?.uri?.toString()}
						className="text-codeseek-accent hover:underline"
					>
						{toolCallReturn?.uri?.fsPath}
					</a>
				</div>
			</div>
		</ToolResult>
	},
	[ToolNameEnum.APPROVE_REQUEST]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		return null
	},
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		return null
	},
	[ToolNameEnum.CTAGS_QUERY]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CTAGS_QUERY]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.CTAGS_QUERY]
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')

		return <ToolResult
			icon="codicon codicon-search"
			title="使用 ctags 工具查询符号定义"
			actionParam={params.symbol}
			actionNumResults={Array.isArray(toolCallReturn) ? toolCallReturn.length : 0}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			<div>
				{Array.isArray(toolCallReturn) && toolCallReturn.length > 0 ? (
					toolCallReturn.map((symbolDefinition, i) => (
						<div key={i} className="pl-2 py-1 mb-1">
							<div className="flex items-center gap-1">
								{symbolDefinition.path && (
									<VSCodeFileIcon uri={URI.file(symbolDefinition.path)} fileKind={FileKind.FILE} />
								)}
								<a
									href="#"
									className="text-codeseek-accent hover:underline"
									onClick={(e) => {
										e.preventDefault();
										if (symbolDefinition.path) {
											const uri = URI.file(symbolDefinition.path);
											const line = symbolDefinition.line || (symbolDefinition.positions && symbolDefinition.positions[1]) || 1;
											commandService.executeCommand('vscode.open', uri, {
												selection: { startLineNumber: line, startColumn: 1, endLineNumber: line, endColumn: 1 },
												preserveFocus: false, // 确保编辑器获得焦点
												preview: true,
											}).then(() => {
												// 使用组合命令确保视图滚动和光标定位
												commandService.executeCommand('revealLine', {
													lineNumber: line,
													at: 'center' // 将目标行居中显示
												}).then(() => {
													// 确保光标定位并可见
													commandService.executeCommand('editor.action.goToLocations',
														uri,
														line,
														1,
														[],
														'goto'
													);
												});
											});
										}
									}}
								>
									{symbolDefinition.name}
								</a>
								<span>
									{symbolDefinition.kind && `(${symbolDefinition.kind})`}
								</span>
							</div>
							{symbolDefinition.path && (
								<div
									className="pl-4"
									title={symbolDefinition.path}
								>
									{getBasename(symbolDefinition.path)}
									{symbolDefinition.line && `:${symbolDefinition.line}`}
								</div>
							)}
						</div>
					))
				) : (
					<div className="pl-2 py-1 italic">
						没有找到相关符号
					</div>
				)}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.CLANGD_QUERY]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CLANGD_QUERY]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.CLANGD_QUERY]
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')

		return <ToolResult
			icon="codicon codicon-search"
			title="使用 clangd 工具查询符号定义"
			actionParam={`${params.filePath}:${params.line}:${params.character}`}
			actionNumResults={Array.isArray(toolCallReturn) ? toolCallReturn.length : 0}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			<div>
				<div className="max-h-[300px] overflow-y-auto pr-1" style={{ scrollbarWidth: 'thin' }}>
					{Array.isArray(toolCallReturn) && toolCallReturn.length > 0 ? (
						toolCallReturn.map((reference, i) => (
							<div key={i} className="pl-2 py-1 mb-1">
								<div className="flex items-center gap-1">
									{reference.uri && (
										<VSCodeFileIcon uri={URI.parse(reference.uri)} fileKind={FileKind.FILE} />
									)}
									<a
										href="#"
										className="text-codeseek-accent hover:underline"
										onClick={(e) => {
											e.preventDefault();
											if (reference.uri) {
												const uri = URI.parse(reference.uri);
												const line = reference.range.start.line + 1 || 1;
												const character = reference.range.start.character + 1 || 1;

												commandService.executeCommand('vscode.open', uri, {
													selection: {
														startLineNumber: line,
														startColumn: character,
														endLineNumber: line,
														endColumn: character
													},
													preserveFocus: false,
													preview: true,
												}).then(() => {
													// 确保视图滚动和光标定位
													commandService.executeCommand('revealLine', {
														lineNumber: line,
														at: 'center' // 将目标行居中显示
													});
												});
											}
										}}
									>
										{getBasename(URI.parse(reference.uri).fsPath)}:{reference.range.start.line + 1}:{reference.range.start.character + 1}
									</a>
								</div>
								{reference.uri && (
									<div className="pl-4 truncate" title={reference.uri}>
										{URI.parse(reference.uri).fsPath}
									</div>
								)}
							</div>
						))
					) : (
						<div className="pl-2 py-1 italic">
							No results found
						</div>
					)}
				</div>
			</div>
		</ToolResult>
	},
	[ToolNameEnum.SHOW_SUMMARY]: ({ message }, messageIdx: number, isStreaming: boolean, containerId?: string, threadId?: string) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.SHOW_SUMMARY]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.SHOW_SUMMARY]
		const accessor = useAccessor()
		const chatThreadsService = accessor.get('IChatThreadService')
		const toolMarkdownIdOffset = 10_000;

		const chatMessageLocation: ChatMessageLocation = {
			containerId: containerId!,
			threadId: threadId!,
			messageIdx: messageIdx! + toolMarkdownIdOffset,
		}

		return (
			<ToolResult
				title={params.summary}
				toolCallResult={message.toolCallResult}
				isStreaming={isStreaming}
			>
				<ChatMarkdownRender string={params.detail ?? ''} chatMessageLocation={chatMessageLocation} />
			</ToolResult>
		);
	},
	[ToolNameEnum.SHOW_CONTENT]: ({ message }, messageIdx: number, isStreaming: boolean, containerId?: string, threadId?: string) => {
		return null;
	},
	[ToolNameEnum.CODEBASE_SEARCH]: ({ message }, messageIdx: number, isStreaming: boolean) => {
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CODEBASE_SEARCH]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.CODEBASE_SEARCH]
		return <ToolResult
			icon="codicon codicon-search"
			title="Search Codebase"
			actionParam={params.query}
			toolCallResult={message.toolCallResult}
			isStreaming={isStreaming}
		>
			{Array.isArray(toolCallReturn) && toolCallReturn.length > 0 ? (
				<FileItemList files={toolCallReturn.map(result => ({ uri: result.uri, range: result.range }))} className={{ hover: 'text-codeseek-fg-1', normal: 'text-codeseek-fg-3' }} />
			) : (
				null
			)}
		</ToolResult>
	},
	[ToolNameEnum.EDIT_FILE]: ({ message }, messageIdx: number) => {
		return null
	}
}
