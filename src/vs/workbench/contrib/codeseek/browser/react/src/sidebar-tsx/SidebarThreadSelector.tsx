/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { ChatContainers } from '../../.././chatThreadType.js';
import { useAccessor, useChatContainerState } from '../util/services.js';
import { ButtonClose, ButtonDelete, IconX } from './SidebarChat.js';
import { useState } from 'react';


const truncate = (s: string) => {
	let len = s.length
	const TRUNC_AFTER = 16
	if (len >= TRUNC_AFTER)
		s = s.substring(0, TRUNC_AFTER) + '...'
	return s
}


export const SidebarThreadSelector = () => {
	const containerState = useChatContainerState()
	const [hoveredThreadId, setHoveredThreadId] = useState<string | null>(null)

	const accessor = useAccessor()
	const chatThreadsService = accessor.get('IChatThreadService')
	const sidebarStateService = accessor.get('ISidebarStateService')

	const { allContainers } = containerState

	const summaryThreadIds = (allContainers: ChatContainers) => {
		const result: {containerId: string, threadId: string}[] = []
		for (const containerId of Object.keys(allContainers ?? {})) {
			const { allThreads } = allContainers[containerId].threadsState
			for (const threadId of Object.keys(allThreads ?? {})) {
				if (allThreads![threadId].messagesLength !== 0) {
					result.push({containerId, threadId})
				}
			}
		}
		return result.sort((a, b) => allContainers[b.containerId]!.threadsState.allThreads[b.threadId].lastModified > allContainers[a.containerId]!.threadsState.allThreads[a.threadId].lastModified ? -1 : 1)
	}

	// sorted by most recent to least recent
	const sortedThreadIds : {containerId: string, threadId: string}[] = summaryThreadIds(allContainers)

	const onSubmit = (containerId: string, pastThreadId: string) => {
		let targetContainerId;
		if (Object.keys(chatThreadsService.containerState.allContainers).includes(containerId)) {
			targetContainerId = containerId;
			if (!chatThreadsService.isFocusingContainer(containerId)) {
				chatThreadsService.switchToContainer(containerId)
			}
		} else {
			targetContainerId = chatThreadsService.getCurrentContainerId();
		}
		chatThreadsService.switchToThread(containerId, pastThreadId, targetContainerId)
		sidebarStateService.setState({ isHistoryOpen: false })
	}

	const onDelete = (e: React.MouseEvent, containerId: string, threadId: string) => {
		e.stopPropagation(); // 防止点击事件冒泡触发onSubmit
		chatThreadsService.deleteThread(containerId, threadId);
	}

	const clearAllThreads = () => {
		if (confirm('确定要清除所有聊天历史记录吗？')) {
			console.log('Clear operation started');

			// 使用当前容器ID，确保与SidebarChat中的容器ID匹配
			const currentContainerId = chatThreadsService.getCurrentContainerId();

			// 删除所有线程
			sortedThreadIds.forEach(({containerId, threadId}) => {
				chatThreadsService.deleteThread(containerId, threadId);
			});

			// 多种方式尝试恢复聚焦
			if (currentContainerId) {
				console.log('Attempting multiple focus strategies...');

				// 策略1: 立即触发聚焦事件
				setTimeout(() => {
					console.log('Strategy 1: Triggering focus event');
					sidebarStateService.fireFocusChat(currentContainerId);
				}, 50);

				// 策略2: 直接查找并聚焦textarea
				setTimeout(() => {
					console.log('Strategy 2: Direct textarea focus');
					const textareas = document.querySelectorAll('textarea');
					console.log('Found textareas:', textareas.length);

					for (let i = 0; i < textareas.length; i++) {
						const textarea = textareas[i];
						if (textarea.placeholder && (textarea.placeholder.includes('select') || textarea.placeholder.includes('mention'))) {
							console.log('Found main textarea, attempting focus...');
							textarea.focus();

							// 验证聚焦结果并测试输入
							setTimeout(() => {
								const isFocused = document.activeElement === textarea;
								console.log('Direct focus result:', { isFocused, activeElement: document.activeElement });

								// 测试是否可以输入
								if (isFocused) {
									console.log('Testing input capability...');
									const originalValue = textarea.value;
									textarea.value = 'test';
									console.log('Input test:', {
										originalValue,
										newValue: textarea.value,
										canInput: textarea.value === 'test'
									});
									// 恢复原值
									textarea.value = originalValue;
								}
							}, 10);
							break;
						}
					}
				}, 200);

				// 策略3: 再次触发聚焦事件（以防第一次失败）
				setTimeout(() => {
					console.log('Strategy 3: Second focus event trigger');
					sidebarStateService.fireFocusChat(currentContainerId);
				}, 300);
			}
		}
	}

	return (
		<div className="flex pt-1 pb-2 flex-col gap-y-1 max-h-[400px] overflow-y-auto">

			<div className="w-full relative flex justify-center items-center">
				<span
					className="absolute left-3 text-xs text-codeseek-fg-3 hover:text-codeseek-fg-1 cursor-pointer"
					onClick={clearAllThreads}
				>
					Clear
				</span>
				{/* title */}
				<h3 className='flex justify-center items-center h-full font-bold text-lg'>{`History`}</h3>
				{/* Clear button on left side of X button */}
				{/* X button at top right */}
				<ButtonClose
					className='absolute top-0.5 right-3'
					style={{
						fontSize: '14px',
					}}
					onClick={() => sidebarStateService.setState({ isHistoryOpen: false })}
				/>
			</div>
			<hr className='border-codeseek-border-3'/>
			{/* a list of all the past threads */}
			<div className="px-1">
				<ul className="flex flex-col gap-y-0.5 overflow-y-auto list-disc">

					{sortedThreadIds.length === 0

						? <div key="nothreads" className="text-center text-codeseek-fg-3 brightness-90 text-sm">{`There are no chat threads yet.`}</div>

						: sortedThreadIds.map(({containerId, threadId}) => {
							if (!allContainers) {
								return <li key="error" className="text-codeseek-warning">{`Error accessing chat history.`}</li>;
							}
							const threadsState = allContainers[containerId].threadsState
							const pastThread = threadsState.allThreads[threadId];

							const firstMsg = pastThread.firstUserMessage
							const numMessages = pastThread.messagesLength;

							return (
								<li key={pastThread.id}>
									<button
										type='button'
										className={`
										hover:bg-codeseek-bg-5
										px-2
										${threadsState.currentThreadId === pastThread.id ? 'bg-codeseek-bg-1' : ''}
										rounded-sm py-1
										w-full
										text-left
										text-sm
										${hoveredThreadId === pastThread.id ? 'text-codeseek-fg-1 font-medium' : 'text-codeseek-fg-3'}
										flex items-center
										justify-between
									`}
										onClick={() => onSubmit(containerId, pastThread.id)}
										title={new Date(pastThread.createdAt).toLocaleString()}
										onMouseEnter={() => setHoveredThreadId(pastThread.id)}
										onMouseLeave={() => setHoveredThreadId(null)}
									>
										<div className='flex items-center gap-x-1 overflow-hidden max-w-[calc(100%-24px)]'>
											<div className='truncate'>{`${firstMsg}`}</div>
											{/* <div className='flex-shrink-0'>{`\u00A0(${numMessages})`}</div> */}
										</div>
										{hoveredThreadId === pastThread.id && (
											<ButtonDelete
												className='cursor-pointer z-1 -ml-1 scale-75'
												onClick={(e) => onDelete(e, containerId, pastThread.id)}
											/>
										)}
									</button>
								</li>
							);
						})
					}
				</ul>
			</div>

		</div>
	)
}
