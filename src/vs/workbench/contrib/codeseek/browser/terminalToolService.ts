
import { Disposable } from '../../../../base/common/lifecycle.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { TerminalCapability } from '../../../../platform/terminal/common/capabilities/capabilities.js';
import { TerminalLocation } from '../../../../platform/terminal/common/terminal.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ICreateTerminalOptions, ITerminalEditorService, ITerminalGroupService } from '../../terminal/browser/terminal.js';
import { ITerminalInstance, ITerminalService } from '../../terminal/browser/terminal.js';
import { CancellationToken } from '../../../../base/common/cancellation.js';
import { get<PERSON><PERSON><PERSON>ri } from '../common/helpers/path.js';
import { removeAnsiEscapeCodes } from '../../../../base/common/strings.js';

const MAX_TERMINAL_CHARS = 100_000

export interface ITerminalToolService {
	readonly _serviceBrand: undefined;
	runCommandOnNewInstance(command: string, workdir?: string, cancellationToken?: CancellationToken): Promise<string>;
}

export const ITerminalToolService = createDecorator<ITerminalToolService>('terminalToolService');

export class TerminalToolService extends Disposable implements ITerminalToolService {
	readonly _serviceBrand: undefined;
	private readonly workspaceDir?: string;
	constructor(
		@ITerminalService private terminalService: ITerminalService,
		@IWorkspaceContextService workspaceContextService: IWorkspaceContextService,
		@ITerminalEditorService private terminalEditorService: ITerminalEditorService,
		@ITerminalGroupService private terminalGroupService: ITerminalGroupService,
	) {
		super()
		const { workspaceUri } = getWorkspaceUri(workspaceContextService);
		this.workspaceDir = workspaceUri?.path
	}

	async runCommandOnNewInstance(command: string, workdir?: string, cancellationToken?: CancellationToken): Promise<string> {
		return new Promise(async (resolve, reject) => {
			// 准备终端启动配置
			const shellLaunchConfig: ICreateTerminalOptions = {
				cwd: workdir || this.workspaceDir, // 工作目录
				config: {
					name: 'Flow',
				}
			};

			// 创建终端实例
			const terminalInstance = await this.terminalService.createTerminal(shellLaunchConfig);

			// 发送命令到终端实例
			await this.show(terminalInstance)

			terminalInstance.sendText(`${command}\n`, true); // true 表示执行命令而不是仅插入


			// onDidEndTerminalShellExecution
			const commandDetectionEndEvent = this._store.add(this.terminalService.createOnInstanceCapabilityEvent(TerminalCapability.CommandDetection, e => e.onCommandFinished));
			this._store.add(commandDetectionEndEvent.event(e => {
				const instanceId = e.instance.instanceId;
				if (instanceId === terminalInstance.instanceId) {
					// Send end in a microtask to ensure the data events are sent first
					setTimeout(() => {
						const output = this.readTerminal(terminalInstance)
						resolve(output)
					});
				}
			}));

			// 添加对cancellationToken的监控
			if (cancellationToken) {
				cancellationToken.onCancellationRequested(() => {
					const output = this.readTerminal(terminalInstance)
					resolve(output)
				});
			}
		})
	}
	async show(terminalInstance: ITerminalInstance | undefined, preserveFocus: boolean = false): Promise<void> {
		if (terminalInstance) {
			this.terminalService.setActiveInstance(terminalInstance);
			if (terminalInstance.target === TerminalLocation.Editor) {
				await this.terminalEditorService.revealActiveEditor(preserveFocus);
			} else {
				await this.terminalGroupService.showPanel(!preserveFocus);
			}
		}
	}
	private readTerminal(terminalInstance: ITerminalInstance) {

		// Ensure the xterm.js instance has been created – otherwise we cannot access the buffer.
		if (!terminalInstance.xterm) {
			throw new Error('Read Terminal: The requested terminal has not yet been rendered and therefore has no scrollback buffer available.');
		}

		// Collect lines from the buffer iterator (oldest to newest)
		const lines: string[] = [];
		for (const line of terminalInstance.xterm.getBufferReverseIterator()) {
			lines.unshift(line);
		}
		// Clean up command separation
		const lastLine = lines.pop()?.trim()
		let terminalContents = lines.join("\n")
		if (lastLine) {
			let i = lines.length - 1
			while (i >= 0 && !lines[i].trim().startsWith(lastLine)) {
				i--
			}
			terminalContents = lines.slice(Math.max(i, 0)).join("\n")
		}
		let result = removeAnsiEscapeCodes(terminalContents);

		if (result.length > MAX_TERMINAL_CHARS) {
			const half = MAX_TERMINAL_CHARS / 2;
			result = result.slice(0, half) + '\n...\n' + result.slice(result.length - half);
		}

		return result
	};
}

registerSingleton(ITerminalToolService, TerminalToolService, InstantiationType.Eager);
